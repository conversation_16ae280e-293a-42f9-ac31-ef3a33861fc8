#!/usr/bin/env python3
"""
Test script to demonstrate the parallel processing functionality
without external dependencies.
"""

import asyncio
import time
import logging
from pathlib import Path
from typing import Dict, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MockRateLimiter:
    """Mock rate limiter to demonstrate functionality."""
    
    def __init__(self, max_calls_per_minute: int = 18):
        self.max_calls_per_minute = max_calls_per_minute
        self.calls = []
        self.lock = asyncio.Lock()
    
    async def acquire(self):
        """Acquire permission to make an API call."""
        async with self.lock:
            now = time.time()
            # Remove calls older than 1 minute
            self.calls = [call_time for call_time in self.calls if now - call_time < 60]
            
            # If we're at the limit, wait until we can make another call
            if len(self.calls) >= self.max_calls_per_minute:
                # Wait until the oldest call is more than 1 minute old
                oldest_call = min(self.calls)
                wait_time = 60 - (now - oldest_call) + 0.1  # Add small buffer
                if wait_time > 0:
                    logger.info(f"Rate limit reached. Waiting {wait_time:.1f} seconds...")
                    await asyncio.sleep(wait_time)
                    # Refresh the calls list after waiting
                    now = time.time()
                    self.calls = [call_time for call_time in self.calls if now - call_time < 60]
            
            # Record this call
            self.calls.append(now)

class MockAnalyzer:
    """Mock analyzer to demonstrate parallel processing."""
    
    def __init__(self, max_workers: int = 4, max_calls_per_minute: int = 18):
        self.max_workers = max_workers
        self.rate_limiter = MockRateLimiter(max_calls_per_minute)
    
    async def mock_analyze_file(self, file_path: Path) -> Dict:
        """Mock file analysis that simulates API call."""
        # Wait for rate limiter permission
        await self.rate_limiter.acquire()
        
        # Simulate processing time
        await asyncio.sleep(0.5)  # Simulate API call time
        
        return {
            "file_path": str(file_path),
            "score": 5,
            "reasoning": f"Mock analysis of {file_path.name}",
            "keywords_found": ["mock", "test"],
            "confidence": "high",
            "error": None
        }
    
    async def process_file_worker(self, file_path: Path, semaphore: asyncio.Semaphore) -> Dict:
        """Worker function to process a single file with concurrency control."""
        async with semaphore:
            try:
                result = await self.mock_analyze_file(file_path)
                logger.info(f"Processed: {result['file_path']} | Score: {result['score']}/10")
                return result
            except Exception as e:
                logger.error(f"Failed to analyze {file_path}: {e}")
                return {
                    "file_path": str(file_path),
                    "score": 1,
                    "reasoning": f"Analysis failed: {str(e)}",
                    "keywords_found": [],
                    "confidence": "low",
                    "error": str(e)
                }
    
    async def analyze_files_async(self, file_paths: List[Path]) -> List[Dict]:
        """Analyze multiple files in parallel."""
        logger.info(f"Processing {len(file_paths)} files with {self.max_workers} workers")
        
        # Create semaphore to limit concurrent workers
        semaphore = asyncio.Semaphore(self.max_workers)
        
        # Create tasks for all files
        tasks = [
            self.process_file_worker(file_path, semaphore)
            for file_path in file_paths
        ]
        
        # Process all files concurrently
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # Filter out exceptions and convert them to error results
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Task failed for {file_paths[i]}: {result}")
                processed_results.append({
                    "file_path": str(file_paths[i]),
                    "score": 1,
                    "reasoning": f"Task failed: {str(result)}",
                    "keywords_found": [],
                    "confidence": "low",
                    "error": str(result)
                })
            else:
                processed_results.append(result)
        
        logger.info(f"Completed processing {len(processed_results)} files in {end_time - start_time:.2f} seconds")
        return processed_results

async def main():
    """Test the parallel processing functionality."""
    # Create some mock file paths
    mock_files = [
        Path(f"mock_file_{i}.txt") for i in range(1, 21)  # 20 mock files
    ]
    
    # Test with different configurations
    configs = [
        {"max_workers": 1, "max_calls_per_minute": 18, "name": "Sequential (1 worker)"},
        {"max_workers": 4, "max_calls_per_minute": 18, "name": "Parallel (4 workers)"},
        {"max_workers": 8, "max_calls_per_minute": 18, "name": "Parallel (8 workers)"},
    ]
    
    for config in configs:
        print(f"\n{'='*60}")
        print(f"Testing: {config['name']}")
        print(f"{'='*60}")
        
        analyzer = MockAnalyzer(
            max_workers=config['max_workers'],
            max_calls_per_minute=config['max_calls_per_minute']
        )
        
        start_time = time.time()
        results = await analyzer.analyze_files_async(mock_files)
        end_time = time.time()
        
        print(f"Processed {len(results)} files in {end_time - start_time:.2f} seconds")
        print(f"Average time per file: {(end_time - start_time) / len(results):.2f} seconds")
        
        # Show rate limiting in action
        calls_per_minute = len(results) / ((end_time - start_time) / 60)
        print(f"Effective rate: {calls_per_minute:.1f} calls per minute")

if __name__ == "__main__":
    asyncio.run(main())
